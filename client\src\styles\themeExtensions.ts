import { Theme } from '@mui/material/styles';

// =============================================================================
// THEME EXTENSIONS
// =============================================================================

/**
 * Extended spacing system for consistent layout
 */
export const extendedSpacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

/**
 * Extended shadow system for depth hierarchy
 */
export const extendedShadows = {
  card: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  cardHover: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  cardElevated: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  modal: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  dropdown: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
};

/**
 * Extended border radius system
 */
export const extendedBorderRadius = {
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,
};

/**
 * Priority color system - Updated to use new palette colors
 */
export const priorityColors = {
  low: {
    main: '#57e2e0', // Quaternary color - calm, low priority
    light: '#7ee8e6',
    dark: '#3dd9d6',
    contrastText: '#000000',
  },
  medium: {
    main: '#fde587', // Accent color - attention-grabbing, medium priority
    light: '#fdeaa1',
    dark: '#fcdf6d',
    contrastText: '#000000',
  },
  high: {
    main: '#a480f9', // Highlights color - urgent, high priority
    light: '#b699fa',
    dark: '#9267f8',
    contrastText: '#ffffff',
  },
};

/**
 * Status color system - Updated to use new palette colors
 */
export const statusColors = {
  pending: {
    main: '#6B7280', // Keep neutral gray for pending
    light: '#D1D5DB',
    dark: '#374151',
    contrastText: '#ffffff',
  },
  active: {
    main: '#02649c', // Primary color - active states
    light: '#4a8bc2',
    dark: '#01456d',
    contrastText: '#ffffff',
  },
  completed: {
    main: '#57e2e0', // Quaternary color - success/completed
    light: '#7ee8e6',
    dark: '#3dd9d6',
    contrastText: '#000000',
  },
  cancelled: {
    main: '#a480f9', // Highlights color - cancelled/error states
    light: '#b699fa',
    dark: '#9267f8',
    contrastText: '#ffffff',
  },
};

/**
 * Extended typography variants
 */
export const extendedTypography = {
  stat: {
    fontSize: '2.5rem',
    fontWeight: 700,
    lineHeight: 1.2,
  },
  cardTitle: {
    fontSize: '1.125rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  cardSubtitle: {
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: 1.4,
  },
  caption: {
    fontSize: '0.75rem',
    fontWeight: 400,
    lineHeight: 1.4,
  },
  overline: {
    fontSize: '0.75rem',
    fontWeight: 600,
    lineHeight: 1.4,
    textTransform: 'uppercase' as const,
    letterSpacing: '0.05em',
  },
};

/**
 * Animation durations and easings
 */
export const animations = {
  duration: {
    fastest: 100,
    fast: 200,
    normal: 300,
    slow: 500,
    slowest: 800,
  },
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
  },
};

/**
 * Layout breakpoints and container sizes
 */
export const layoutConfig = {
  containerMaxWidth: {
    xs: '100%',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  },
  sidebarWidth: {
    collapsed: 64,
    expanded: 280,
  },
  headerHeight: {
    mobile: 56,
    desktop: 64,
  },
};

// =============================================================================
// THEME UTILITIES
// =============================================================================

/**
 * Get priority color from extended palette
 */
export const getPriorityColor = (theme: Theme, priority: 'low' | 'medium' | 'high') => {
  return priorityColors[priority];
};

/**
 * Get status color from extended palette
 */
export const getStatusColor = (theme: Theme, status: 'pending' | 'active' | 'completed' | 'cancelled') => {
  return statusColors[status];
};

/**
 * Create responsive value helper
 */
export const createResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
}) => {
  return (theme: Theme) => ({
    ...(values.xs !== undefined && { [theme.breakpoints.up('xs')]: values.xs }),
    ...(values.sm !== undefined && { [theme.breakpoints.up('sm')]: values.sm }),
    ...(values.md !== undefined && { [theme.breakpoints.up('md')]: values.md }),
    ...(values.lg !== undefined && { [theme.breakpoints.up('lg')]: values.lg }),
    ...(values.xl !== undefined && { [theme.breakpoints.up('xl')]: values.xl }),
  });
};

/**
 * Create hover state helper
 */
export const createHoverState = (theme: Theme, styles: Record<string, any>) => ({
  transition: theme.transitions.create(Object.keys(styles), {
    duration: animations.duration.fast,
    easing: animations.easing.easeOut,
  }),
  '&:hover': styles,
});

/**
 * Create hover effects for interactive elements
 */
export const createHoverEffect = (theme: Theme, intensity: 'subtle' | 'medium' | 'strong' = 'medium') => {
  const effects = {
    subtle: {
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      '&:hover': {
        transform: 'translateY(-1px)',
        boxShadow: extendedShadows.cardHover,
      },
    },
    medium: {
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: extendedShadows.cardElevated,
      },
    },
    strong: {
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: extendedShadows.modal,
      },
    },
  };

  return effects[intensity];
};

/**
 * Create focus state helper
 */
export const createFocusState = (theme: Theme, color?: string) => ({
  '&:focus-visible': {
    outline: `2px solid ${color || theme.palette.primary.main}`,
    outlineOffset: 2,
    borderRadius: theme.shape.borderRadius,
  },
});

/**
 * Create disabled state helper
 */
export const createDisabledState = (theme: Theme) => ({
  '&:disabled, &.Mui-disabled': {
    opacity: 0.6,
    cursor: 'not-allowed',
    pointerEvents: 'none',
  },
});

/**
 * Create loading state helper
 */
export const createLoadingState = (theme: Theme) => ({
  '&.loading': {
    opacity: 0.7,
    cursor: 'wait',
    pointerEvents: 'none',
  },
});

/**
 * Create truncated text helper
 */
export const createTruncatedText = (lines: number = 1) => ({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: lines,
  WebkitBoxOrient: 'vertical' as const,
  ...(lines === 1 && {
    whiteSpace: 'nowrap' as const,
    display: 'block',
  }),
});

/**
 * Create glass morphism effect
 */
export const createGlassMorphism = (theme: Theme, opacity: number = 0.1) => ({
  backgroundColor: `rgba(255, 255, 255, ${opacity})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid rgba(255, 255, 255, ${opacity * 2})`,
});

/**
 * Create gradient background
 */
export const createGradientBackground = (
  direction: string = 'to right',
  colors: string[] = ['#3B82F6', '#8B5CF6']
) => ({
  background: `linear-gradient(${direction}, ${colors.join(', ')})`,
});

// =============================================================================
// COMPONENT VARIANTS
// =============================================================================

/**
 * Card variants for different use cases
 */
export const cardVariants = {
  elevated: (theme: Theme) => ({
    boxShadow: extendedShadows.cardElevated,
    '&:hover': {
      boxShadow: extendedShadows.modal,
    },
  }),
  outlined: (theme: Theme) => ({
    border: `1px solid ${theme.palette.divider}`,
    boxShadow: 'none',
  }),
  filled: (theme: Theme) => ({
    backgroundColor: theme.palette.background.default,
    boxShadow: 'none',
  }),
};

/**
 * Button variants for different contexts
 */
export const buttonVariants = {
  gradient: (theme: Theme) => ({
    background: createGradientBackground(),
    color: theme.palette.common.white,
    '&:hover': {
      background: createGradientBackground('to right', ['#1E40AF', '#7C3AED']),
    },
  }),
  glass: (theme: Theme) => ({
    ...createGlassMorphism(theme),
    '&:hover': {
      backgroundColor: `rgba(255, 255, 255, 0.2)`,
    },
  }),
};

// =============================================================================
// EXPORTS
// =============================================================================

export const themeExtensions = {
  extendedSpacing,
  extendedShadows,
  extendedBorderRadius,
  priorityColors,
  statusColors,
  extendedTypography,
  animations,
  layoutConfig,
  cardVariants,
  buttonVariants,
};
