import React from 'react';
import { Button, Typography } from '@mui/material';
import { 
  Styled<PERSON>ontainer,
  FlexContainer,
  GridContainer,
  StyledCard,
  StatCard,
  StatCardContent,
  IconTypography,
  PriorityChip,
  ActivityCard,
  ActivityHeader,
  ActivityTitle,
  ActivityMeta,
  ActivityDescription,
  StatsGrid,
  StatIcon,
  StatNumber,
  StatLabel,
} from './DashboardComponents';

// =============================================================================
// USAGE EXAMPLES FOR STYLED COMPONENTS
// =============================================================================

/**
 * Example: Stats Grid with Hover Effects
 */
export const StatsExample: React.FC = () => (
  <StatsGrid>
    <StatCard>
      <StatCardContent>
        <StatIcon>📧</StatIcon>
        <StatNumber>12</StatNumber>
        <StatLabel>New Messages</StatLabel>
      </StatCardContent>
    </StatCard>
    
    <StatCard>
      <StatCardContent>
        <StatIcon>⏰</StatIcon>
        <StatNumber>5</StatNumber>
        <StatLabel>Upcoming Events</StatLabel>
      </StatCardContent>
    </StatCard>
    
    <StatCard>
      <StatCardContent>
        <StatIcon>🚨</StatIcon>
        <StatNumber>2</StatNumber>
        <StatLabel>Urgent Items</StatLabel>
      </StatCardContent>
    </StatCard>
  </StatsGrid>
);

/**
 * Example: Activity Card with Priority
 */
export const ActivityExample: React.FC = () => (
  <StyledContainer spacing={3}>
    <ActivityCard priority="high">
      <ActivityHeader>
        <ActivityTitle>Soccer Practice Cancelled</ActivityTitle>
        <PriorityChip priority="high" label="High" size="small" />
      </ActivityHeader>
      <ActivityMeta>
        👤 Jake • 📅 2024-02-12
      </ActivityMeta>
      <ActivityDescription>
        Due to weather conditions, soccer practice is cancelled today. 
        Please check the team app for updates on rescheduling.
      </ActivityDescription>
    </ActivityCard>

    <ActivityCard priority="medium">
      <ActivityHeader>
        <ActivityTitle>Picture Day - Lincoln Elementary</ActivityTitle>
        <PriorityChip priority="medium" label="Medium" size="small" />
      </ActivityHeader>
      <ActivityMeta>
        👤 Emma • 📅 2024-02-15
      </ActivityMeta>
      <ActivityDescription>
        School picture day. Remember to dress Emma in her blue dress.
        Photos will be taken between 9:00 AM and 11:00 AM.
      </ActivityDescription>
    </ActivityCard>
  </StyledContainer>
);

/**
 * Example: Flexible Layout Components
 */
export const LayoutExample: React.FC = () => (
  <StyledContainer spacing={4}>
    {/* Flex container with space-between */}
    <FlexContainer justify="space-between" align="center" gap={2}>
      <Typography variant="h5">Dashboard Overview</Typography>
      <Button variant="contained">Refresh</Button>
    </FlexContainer>

    {/* Grid container with responsive columns */}
    <GridContainer 
      columns={{ xs: 1, sm: 2, md: 3 }} 
      gap={3}
      minWidth="250px"
    >
      <StyledCard interactive>
        <StatCardContent>
          <IconTypography size="large">📊</IconTypography>
          <Typography variant="h6">Analytics</Typography>
          <Typography variant="body2" color="text.secondary">
            View detailed analytics and insights
          </Typography>
        </StatCardContent>
      </StyledCard>

      <StyledCard interactive>
        <StatCardContent>
          <IconTypography size="large">⚙️</IconTypography>
          <Typography variant="h6">Settings</Typography>
          <Typography variant="body2" color="text.secondary">
            Manage your preferences and configuration
          </Typography>
        </StatCardContent>
      </StyledCard>

      <StyledCard interactive>
        <StatCardContent>
          <IconTypography size="large">👥</IconTypography>
          <Typography variant="h6">Team</Typography>
          <Typography variant="body2" color="text.secondary">
            Collaborate with your team members
          </Typography>
        </StatCardContent>
      </StyledCard>
    </GridContainer>
  </StyledContainer>
);

/**
 * Example: Before and After Comparison
 */
export const ComparisonExample: React.FC = () => (
  <StyledContainer spacing={4}>
    <Typography variant="h4" gutterBottom>
      Before vs After: Styled Components
    </Typography>

    {/* BEFORE: Using sx prop */}
    <div>
      <Typography variant="h6" gutterBottom>
        ❌ Before (sx prop approach):
      </Typography>
      <pre style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '16px', 
        borderRadius: '8px',
        fontSize: '14px',
        overflow: 'auto'
      }}>
{`<Box sx={{ 
  p: 3, 
  display: 'flex', 
  justifyContent: 'space-between',
  alignItems: 'center',
  mb: 3 
}}>
  <Typography variant="h5">Title</Typography>
  <Button variant="contained">Action</Button>
</Box>`}
      </pre>
    </div>

    {/* AFTER: Using styled components */}
    <div>
      <Typography variant="h6" gutterBottom>
        ✅ After (styled components approach):
      </Typography>
      <pre style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '16px', 
        borderRadius: '8px',
        fontSize: '14px',
        overflow: 'auto'
      }}>
{`<FlexContainer 
  justify="space-between" 
  align="center" 
  gap={2}
  style={{ marginBottom: 24 }}
>
  <Typography variant="h5">Title</Typography>
  <Button variant="contained">Action</Button>
</FlexContainer>`}
      </pre>
    </div>

    <Typography variant="body1" color="text.secondary">
      Benefits: Better performance, reusability, type safety, and cleaner component code.
    </Typography>
  </StyledContainer>
);

/**
 * Example: Theme Integration
 */
export const ThemeIntegrationExample: React.FC = () => (
  <StyledContainer spacing={3}>
    <Typography variant="h6" gutterBottom>
      Theme Integration Examples
    </Typography>
    
    {/* Priority colors */}
    <FlexContainer gap={2} wrap>
      <PriorityChip priority="low" label="Low Priority" />
      <PriorityChip priority="medium" label="Medium Priority" />
      <PriorityChip priority="high" label="High Priority" />
    </FlexContainer>

    {/* Interactive cards */}
    <GridContainer columns={{ xs: 1, sm: 2 }} gap={2}>
      <StyledCard interactive priority="medium">
        <StatCardContent>
          <Typography variant="h6">Interactive Card</Typography>
          <Typography variant="body2" color="text.secondary">
            Hover to see the effect
          </Typography>
        </StatCardContent>
      </StyledCard>

      <StyledCard selected>
        <StatCardContent>
          <Typography variant="h6">Selected Card</Typography>
          <Typography variant="body2" color="text.secondary">
            This card is in selected state
          </Typography>
        </StatCardContent>
      </StyledCard>
    </GridContainer>
  </StyledContainer>
);

// =============================================================================
// COMPLETE DEMO COMPONENT
// =============================================================================

export const StyledComponentsDemo: React.FC = () => (
  <StyledContainer spacing={6}>
    <Typography variant="h3" gutterBottom>
      Styled Components Library Demo
    </Typography>
    
    <StatsExample />
    <ActivityExample />
    <LayoutExample />
    <ComparisonExample />
    <ThemeIntegrationExample />
  </StyledContainer>
);
