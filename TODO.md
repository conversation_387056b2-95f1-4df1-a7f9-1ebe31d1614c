TODO
---

MVP

- [ ] Research: Decide and fully implement ORM - Prisma or Drizzle?
- [ ] Feature: UI - Manage children - CRUD. Basic info only: First name, age, current grade, school name
- [ ] Feature: Github action deployments / releases
- [ ] Feature: Add version tag to site (footer)
- [ ] Feature: Way to generate view unique email for entire account (or each child gets one?) (<EMAIL>)
- [ ] Research: Select and purchase credit for LLM service (for MVP)
- [ ] Feature: Email processing service with abstracted AI processing model - would be nice if it could parallize
- [ ] Research: Thought needs to happen around what type of data to explain to the AI to extract (important dates, requests, tasks, how to differentiate between different kids, etc)
- [ ] Feature: Database schema/file structure to store data
- [ ] Feature: UI - Main dashboard - shows events, important dates, messages, "day at a glance"
- [ ] Feature: UI - Calendar view
- [ ] Feature: UI - Mobile friendly version?
- [ ] Feature: UI - brand, colors, logos, SEO (don't need to finalize, but should pick something/setup infrastructure)
- [ ] Feature: UI - use "styled components" for styling with theming
- [ ] Feature: Basic account profile management
- [ ] Feature: Unit tests
- [ ] Feature: Integration tests - might as well try to automate this (no idea how it works for native mobile apps)
- [ ] Business: Devise MVP plan for what we want to test and the way we'd test it - what does success look like?
    ---> Draft PMF testing plan is in specs/PMF -- Remus and Kev to align on part 1
- [ ] Feature: Public-facing portion of the site (depends on the MVP plan)

Future (Post MVP)

- [ ] Continue flushing out this TODO list...
- [ ] Address any scaling issues from MVP - might involve rewriting things
- [ ] Feature: Ability to link account with another parent/person
- [ ] Business: Business plan
- [ ] Business: Company, ownership structure, shares, investment plan, employees, etc
- [ ] Business: Marketing plan - see [specs/marketing.md], partnerships with blogs, affiliates, promos?
- [ ] Business: Setup domain email addresses (probably Google workspace?)
- [ ] IT: Cloud setup (GCP?)
- [ ] Feature: Admin dashboard to monitor subscription counts and other metrics
- [ ] Feature: ios/android apps - maybe don't even need web UI, go pure native apps?
- [ ] Business: Determine price of subscription
- [ ] Business: Decide on free vs paid features - maybe free = mobile app ads and limited functionality
- [ ] Business: Subcriptions - ios/android in-app subscriptions? Google/Apple take 30%...
- [ ] Feature: Social logins, 2FA, password resets, etc
- [ ] Business: Determine customer support, gathering customer feedback, etc.
- [ ] Business: Create privacy policy, terms of service, etc
- [ ] Business: Determine accounting plan
