{"name": "pigeon-squad", "version": "1.0.0", "description": "Pigeon Squad - Family Activity Monitor", "private": true, "scripts": {"install:all": "npm install && cd server && npm install && cd ../client && npm install", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "npm run build:server && npm run build:client", "build:server": "cd server && npm run build", "build:client": "cd client && npm run build", "start": "cd server && npm start", "start:server": "cd server && npm start", "start:client": "cd client && npm run preview", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "clean": "rm -rf node_modules server/node_modules client/node_modules server/dist client/dist", "clean:install": "npm run clean && npm run install:all"}, "devDependencies": {"concurrently": "^8.2.2", "@prisma/client": "^6.16.1", "prisma": "^6.16.1"}}