import { styled } from '@mui/material/styles';
import { Box, Container, Grid } from '@mui/material';
import {
  extendedShadows,
  animations,
} from '../../styles/themeExtensions';

// Page Container - Full page wrapper with consistent structure
interface PageContainerProps {
  fullHeight?: boolean;
  centered?: boolean;
  background?: 'default' | 'paper' | 'primary' | 'secondary' | 'gradient';
}

export const PageContainer = styled(Box, {
  shouldForwardProp: (prop) => !['fullHeight', 'centered', 'background'].includes(prop as string),
})<PageContainerProps>(({ theme, fullHeight, centered, background }) => {
  const getBackground = () => {
    switch (background) {
      case 'paper':
        return theme.palette.background.paper;
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'gradient':
        return `linear-gradient(135deg, 
          ${theme.palette.primary.light}20 0%, 
          ${theme.palette.secondary.light}20 50%, 
          ${theme.palette.primary.main}10 100%
        )`;
      default:
        return theme.palette.background.default;
    }
  };

  return {
    minHeight: fullHeight ? '100vh' : 'auto',
    display: centered ? 'flex' : 'block',
    alignItems: centered ? 'center' : 'stretch',
    backgroundColor: getBackground(),
    position: 'relative',
    ...(background === 'gradient' && {
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 20% 80%, ${theme.palette.primary.main}15 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, ${theme.palette.secondary.main}15 0%, transparent 50%)`,
        pointerEvents: 'none',
      },
    }),
  };
});

// Section - Semantic section wrapper with consistent spacing
interface SectionProps {
  spacing?: 'small' | 'medium' | 'large' | 'xlarge';
  background?: 'default' | 'paper' | 'primary' | 'secondary';
  textAlign?: 'left' | 'center' | 'right';
}

export const Section = styled(Box, {
  shouldForwardProp: (prop) => !['spacing', 'background', 'textAlign'].includes(prop as string),
})<SectionProps>(({ theme, spacing = 'medium', background, textAlign }) => {
  const getSpacing = () => {
    switch (spacing) {
      case 'small':
        return theme.spacing(4, 0);
      case 'large':
        return theme.spacing(10, 0);
      case 'xlarge':
        return theme.spacing(12, 0);
      default:
        return theme.spacing(8, 0);
    }
  };

  const getBackground = () => {
    switch (background) {
      case 'paper':
        return theme.palette.background.paper;
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      default:
        return 'transparent';
    }
  };

  return {
    padding: getSpacing(),
    backgroundColor: getBackground(),
    textAlign: textAlign || 'inherit',
    position: 'relative',
    zIndex: 1,
    ...(background === 'primary' && {
      color: theme.palette.primary.contrastText,
    }),
    ...(background === 'secondary' && {
      color: theme.palette.secondary.contrastText,
    }),
  };
});

// Content Container - Responsive container with max-width constraints
interface ContentContainerProps {
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const ContentContainer = styled(Container)<ContentContainerProps>(({ theme, padding = 'medium' }) => {
  const getPadding = () => {
    switch (padding) {
      case 'none':
        return 0;
      case 'small':
        return theme.spacing(2);
      case 'large':
        return theme.spacing(4);
      default:
        return theme.spacing(3);
    }
  };

  return {
    paddingLeft: getPadding(),
    paddingRight: getPadding(),
    position: 'relative',
    zIndex: 1,
  };
});

// Flex Container - Flexible layout container
interface FlexContainerProps {
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  gap?: number | string;
  fullWidth?: boolean;
  fullHeight?: boolean;
}

export const FlexContainer = styled(Box)<FlexContainerProps>(({ 
  theme, 
  direction = 'row', 
  justify = 'flex-start', 
  align = 'stretch', 
  wrap = 'nowrap',
  gap = 0,
  fullWidth,
  fullHeight 
}) => ({
  display: 'flex',
  flexDirection: direction,
  justifyContent: justify,
  alignItems: align,
  flexWrap: wrap,
  gap: typeof gap === 'number' ? theme.spacing(gap) : gap,
  width: fullWidth ? '100%' : 'auto',
  height: fullHeight ? '100%' : 'auto',
}));

// Grid Container - Enhanced grid layout with responsive spacing
interface GridContainerProps {
  spacing?: number;
  columns?: number;
  responsive?: boolean;
  equalHeight?: boolean;
}

export const GridContainer = styled(Grid)<GridContainerProps>(({ theme, equalHeight, responsive }) => ({
  ...(equalHeight && {
    '& > .MuiGrid-item': {
      display: 'flex',
      '& > *': {
        flex: 1,
      },
    },
  }),
  ...(responsive && {
    [theme.breakpoints.down('sm')]: {
      '& > .MuiGrid-item': {
        flexBasis: '100%',
        maxWidth: '100%',
      },
    },
  }),
}));

// Hero Section - Large header section for landing pages
interface HeroSectionProps {
  minHeight?: string | number;
  textAlign?: 'left' | 'center' | 'right';
  background?: 'default' | 'gradient' | 'primary' | 'secondary';
}

export const HeroSection = styled(Box)<HeroSectionProps>(({ theme, minHeight = '100vh', textAlign = 'center', background = 'default' }) => {
  const getBackground = () => {
    switch (background) {
      case 'gradient':
        return `linear-gradient(135deg, 
          ${theme.palette.primary.main}10 0%, 
          ${theme.palette.secondary.main}10 50%, 
          ${theme.palette.background.default} 100%
        )`;
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      default:
        return theme.palette.background.default;
    }
  };

  return {
    minHeight: typeof minHeight === 'number' ? `${minHeight}px` : minHeight,
    display: 'flex',
    alignItems: 'center',
    textAlign,
    background: getBackground(),
    position: 'relative',
    ...(background === 'primary' && {
      color: theme.palette.primary.contrastText,
    }),
    ...(background === 'secondary' && {
      color: theme.palette.secondary.contrastText,
    }),
  };
});

// Card Grid - Specialized grid for card layouts
export const CardGrid = styled(Grid)(({ theme }) => ({
  '& .MuiGrid-item': {
    display: 'flex',
    '& > *': {
      flex: 1,
      height: '100%',
    },
  },
}));

// Sidebar Layout Components
export const SidebarLayout = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
  },
}));

interface SidebarProps {
  width?: number | string;
  background?: 'default' | 'paper' | 'primary';
}

export const Sidebar = styled(Box)<SidebarProps>(({ theme, width = 280, background = 'paper' }) => {
  const getBackground = () => {
    switch (background) {
      case 'primary':
        return theme.palette.primary.main;
      case 'paper':
        return theme.palette.background.paper;
      default:
        return theme.palette.background.default;
    }
  };

  return {
    width: typeof width === 'number' ? `${width}px` : width,
    flexShrink: 0,
    backgroundColor: getBackground(),
    borderRight: `1px solid ${theme.palette.divider}`,
    [theme.breakpoints.down('md')]: {
      width: '100%',
      borderRight: 'none',
      borderBottom: `1px solid ${theme.palette.divider}`,
    },
    ...(background === 'primary' && {
      color: theme.palette.primary.contrastText,
    }),
  };
});

export const MainContent = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
}));

// Centered Content - For centering content both horizontally and vertically
interface CenteredContentProps {
  maxWidth?: number | string;
  padding?: number;
}

export const CenteredContent = styled(Box)<CenteredContentProps>(({ theme, maxWidth, padding = 3 }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  textAlign: 'center',
  padding: theme.spacing(padding),
  ...(maxWidth && {
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    margin: '0 auto',
  }),
}));

// Spacer - For adding consistent spacing between elements
interface SpacerProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  direction?: 'horizontal' | 'vertical';
}

export const Spacer = styled(Box)<SpacerProps>(({ theme, size = 'medium', direction = 'vertical' }) => {
  const getSize = () => {
    switch (size) {
      case 'small':
        return theme.spacing(2);
      case 'large':
        return theme.spacing(6);
      case 'xlarge':
        return theme.spacing(8);
      default:
        return theme.spacing(4);
    }
  };

  return {
    [direction === 'vertical' ? 'height' : 'width']: getSize(),
    flexShrink: 0,
  };
});
